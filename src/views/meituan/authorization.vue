<template>
  <div class="container">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
        <!-- 第一个标签页：授权状态查询 -->
        <a-tab-pane key="unauthorized" title="未授权">
          <div class="batch-controls">
            <a-space>
              <a-button
                :type="batchSearchForm.status === 'unauthorized' ? 'primary' : 'outline'"
                @click="handleBatchFilter('unauthorized')">
                未授权
              </a-button>
              <a-button
                :type="batchSearchForm.status === 'authorized' ? 'primary' : 'outline'"
                @click="handleBatchFilter('authorized')">
                已授权
              </a-button>
            </a-space>
          </div>

          <a-table
            v-bind="batchTableProps"
            v-model:selected-keys="selectedKeys"
            row-key="id"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              onlyCurrent: false,
            }"
            style="margin-top: 20px"
            v-on="batchTableEvent">
            <template #columns>
              <a-table-column title="请求名称" data-index="request_name" />
            </template>
          </a-table>

          <div class="batch-footer">
            <a-space>
              <a-checkbox v-model="selectAll" @change="handleSelectAllChange">全选</a-checkbox>
              <a-button
                type="primary"
                :disabled="selectedKeys.length === 0"
                :loading="batchAuthLoading"
                @click="handleBatchAuth">
                批量授权
              </a-button>
            </a-space>
          </div>
        </a-tab-pane>

        <!-- 第二个标签页：批量授权管理 -->
        <a-tab-pane key="authorized" title="已授权">
          <div class="search-form">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-select v-model="statusSearchForm.type" placeholder="请选择类型" allow-clear style="width: 100%">
                  <a-option value="0">未授权</a-option>
                  <a-option value="1">已授权</a-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="handleStatusSearch">
                    <template #icon>
                      <icon-search />
                    </template>
                    搜索
                  </a-button>
                  <a-button @click="handleStatusReset">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <a-table v-bind="statusTableProps" style="margin-top: 20px" v-on="statusTableEvent">
            <template #columns>
              <a-table-column title="序号" :width="80" align="center">
                <template #cell="{ rowIndex }">
                  {{
                    (statusTableProps.pagination?.current || 1 - 1) * (statusTableProps.pagination?.pageSize || 10) +
                    rowIndex +
                    1
                  }}
                </template>
              </a-table-column>
              <a-table-column title="请求名称" data-index="request_name" />
              <a-table-column title="美团授权状态" data-index="auth_status" align="center">
                <template #cell="{ record }">
                  <a-tag :color="record.auth_status === 'authorized' ? 'green' : 'red'">
                    {{ record.auth_status === 'authorized' ? '已授权' : '未授权' }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="操作时间" data-index="operation_time" />
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import useTableProps from '@/hooks/table-props';
  import { getCustomerAuthList } from '@/api/meituan';

  defineOptions({
    name: 'MeituanAuthorization',
  });

  // 状态查询API函数
  const statusApi = (data: { type: number }) => getCustomerAuthList(data);

  // 批量授权API函数
  const batchApi = (data: { type: number }) => getCustomerAuthList(data);

  // 当前激活的标签页
  const activeTab = ref('status');

  // 第一个标签页：授权状态查询
  const statusSearchForm = reactive({
    type: '0',
  });

  const {
    tableProps: statusTableProps,
    tableEvent: statusTableEvent,
    searchParam: statusSearchParam,
    handleSearch: statusHandleSearch,
    setSearchParam: statusSetSearchParam,
    loadTableList: statusLoadTableList,
  } = useTableProps(() => statusApi({ type: 0 }));

  // 设置状态查询表格的搜索参数
  statusSetSearchParam({
    type: '0',
  });

  // 第二个标签页：批量授权管理
  const batchSearchForm = reactive({
    status: 'unauthorized', // 默认显示未授权
  });

  const {
    tableProps: batchTableProps,
    tableEvent: batchTableEvent,
    searchParam: batchSearchParam,
    handleSearch: batchHandleSearch,
    setSearchParam: batchSetSearchParam,
    loadTableList: batchLoadTableList,
  } = useTableProps(() => batchApi({ type: 0 }));

  // 设置批量授权表格的搜索参数
  batchSetSearchParam({
    status: 'unauthorized',
    type: '0',
  });

  // 批量授权相关状态
  const selectedKeys = ref<(string | number)[]>([]);
  const selectAll = ref(false);
  const batchAuthLoading = ref(false);

  // 标签页切换处理
  const handleTabChange = (key: string | number) => {
    const tabKey = String(key);
    if (tabKey === 'status') {
      statusLoadTableList();
    } else if (tabKey === 'batch') {
      batchLoadTableList();
    }
  };

  // 状态查询相关方法
  const handleStatusSearch = () => {
    statusSearchParam.type = statusSearchForm.type;
    // 更新API调用参数
    statusSetSearchParam({ type: statusSearchForm.type });
    statusHandleSearch();
  };

  const handleStatusReset = () => {
    statusSearchForm.type = '0';
    statusSearchParam.type = '0';
    // 更新API调用参数
    statusSetSearchParam({ type: '0' });
    statusHandleSearch();
  };

  // 批量授权相关方法
  const handleBatchFilter = (status: string) => {
    batchSearchForm.status = status;
    batchSearchParam.status = status;
    // 映射status到type参数: 'unauthorized' -> 0, 'authorized' -> 1
    const type = status === 'authorized' ? 1 : 0;
    // 更新API调用参数
    batchSetSearchParam({ type: type.toString() });
    selectedKeys.value = [];
    selectAll.value = false;
    batchHandleSearch();
  };

  const handleSelectAllChange = (checked: boolean | (string | number | boolean)[]) => {
    const isChecked = Array.isArray(checked) ? checked.length > 0 : checked;
    if (isChecked) {
      selectedKeys.value = batchTableProps.data?.map((item) => item.id) || [];
    } else {
      selectedKeys.value = [];
    }
  };

  const handleBatchAuth = async () => {
    if (selectedKeys.value.length === 0) {
      Message.warning('请选择要授权的项目');
      return;
    }

    batchAuthLoading.value = true;
    try {
      // 模拟批量授权API调用
      await new Promise<void>((resolve) => {
        setTimeout(() => resolve(), 1000);
      });
      Message.success(`成功授权 ${selectedKeys.value.length} 个项目`);
      selectedKeys.value = [];
      selectAll.value = false;
      batchLoadTableList();
    } catch (error) {
      Message.error('批量授权失败');
    } finally {
      batchAuthLoading.value = false;
    }
  };

  // 监听选中项变化，更新全选状态
  watch(
    selectedKeys,
    (newKeys) => {
      const totalCount = batchTableProps.data?.length || 0;
      selectAll.value = totalCount > 0 && newKeys.length === totalCount;
    },
    { deep: true }
  );

  // 初始化加载数据
  onMounted(() => {
    statusLoadTableList();
  });
</script>

<style lang="less" scoped>
  .container {
    padding: 0 20px 20px 20px;
  }

  .search-form {
    padding: 20px;
    background-color: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .batch-controls {
    margin-bottom: 16px;
  }

  .batch-footer {
    margin-top: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
